package site_report

import (
	"errors"
	"time"

	constanta "github.com/Sera-Global/be-nbs-accounting-system/common/const"
	utils "github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/utils"
	sitereport "github.com/Sera-Global/be-nbs-accounting-system/usecase/site_report"
)

func validateGetList(req sitereport.GetListReq) error {
	if req.StartDate == "" {
		return ErrStartDateRequired
	}

	if req.EndDate == "" {
		return ErrEndDateRequired
	}

	// validate start date must be before end date
	if req.ParsedStartDate.After(req.ParsedEndDate) {
		return ErrStartDateAfterEndDate
	}

	return nil
}

func validateBulkUpdate(req sitereport.BulkUpdateReq) error {
	// Validate site_report_ids is required and not empty
	if len(req.SiteReportIDs) == 0 {
		return ErrSiteReportIDsRequired
	}

	// Validate at least one field is provided
	if req.WorkDate == nil && req.IsLocked == nil && req.IsInvoiceIssued == nil {
		return ErrNoFieldsProvided
	}

	// Validate date format if work_date is provided
	if req.WorkDate != nil {
		_, err := time.Parse("2006-01-02", *req.WorkDate)
		if err != nil {
			return ErrInvalidDateFormat
		}
	}

	// Role-based authorization checks
	if req.WorkDate != nil {
		// work_date updates: Only RoleAdmin and RoleSuperAdmin
		if !utils.HasRole(req.UserRoles, constanta.RoleAdmin, constanta.RoleSuperAdmin) {
			return errors.New(constanta.Unauthorized)
		}
	}

	if req.IsLocked != nil {
		if *req.IsLocked {
			// is_locked = true: Only RoleAdmin and RoleSuperAdmin
			if !utils.HasRole(req.UserRoles, constanta.RoleAdmin, constanta.RoleSuperAdmin) {
				return errors.New(constanta.Unauthorized)
			}
		} else {
			// is_locked = false: Only RoleSuperAdmin
			if !utils.HasRole(req.UserRoles, constanta.RoleSuperAdmin) {
				return errors.New(constanta.Unauthorized)
			}
		}
	}

	if req.IsInvoiceIssued != nil && *req.IsInvoiceIssued {
		// is_invoice_issued = true: RoleSubAdmin, RoleAdmin, and RoleSuperAdmin
		if !utils.HasRole(req.UserRoles, constanta.RoleSubAdmin, constanta.RoleAdmin, constanta.RoleSuperAdmin) {
			return errors.New(constanta.Unauthorized)
		}
	}

	return nil
}
