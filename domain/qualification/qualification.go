package qualification

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
)

type (
	QualificationDomainItf interface {
		GetList(ctx context.Context, param GetListReq) ([]Qualification, error)
	}

	QualificationResourceItf interface {
		getList(ctx context.Context, param GetListReq) ([]Qualification, error)
	}
)

// GetList retrieves all qualifications ordered by title.
func (d *QualificationDomain) GetList(ctx context.Context, param GetListReq) ([]Qualification, error) {
	qualifications, err := d.resource.getList(ctx, param)
	if err != nil {
		return []Qualification{}, log.LogError(err, nil)
	}
	return qualifications, nil
}
